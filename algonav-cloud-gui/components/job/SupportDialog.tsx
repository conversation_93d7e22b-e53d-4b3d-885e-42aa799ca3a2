import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  <PERSON>alogContent,
  DialogContentText,
  DialogActions,
  Button,
  IconButton,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField,
  Box,
  Typography
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useCreateTicket } from '@/lib/hooks/useTickets';
import { SupportReasonType } from '../../types/job';



interface SupportDialogProps {
  open: boolean;
  taskId: number | null;
  reason: string;
  additionalInfo: string;
  supportType: 'dataset' | 'job' | 'general';
  datasetName?: string;
  jobId?: string;
  onClose: () => void;
  onReasonChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onAdditionalInfoChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onSubmit?: () => void; // Made optional since we now handle ticket creation internally
}

const SupportDialog: React.FC<SupportDialogProps> = ({
  open,
  taskId,
  reason,
  additionalInfo,
  supportType,
  datasetName,
  jobId,
  onClose,
  onReasonChange,
  onAdditionalInfoChange,
  onSubmit
}) => {
  const createTicket = useCreateTicket();

  const prepareTargets = () => {
    const targets = [];

    if (taskId) {
      targets.push({
        target_type: 'task' as const,
        target_id: taskId,
        name: datasetName
      });
    }

    if (jobId) {
      targets.push({
        target_type: 'job' as const,
        target_id: parseInt(jobId),
        name: `Job ${jobId}`
      });
    }

    return targets;
  };



  const handleSupportRequestSubmit = async () => {
    // Create ticket from the traditional support request form
    const targets = prepareTargets();

    // Generate title based on selected reason and context
    const getReasonLabel = (reasonValue: string) => {
      const reasonLabels: { [key: string]: string } = {
        // Dataset/Task reasons
        'processing_terminated': 'Processing terminated with error',
        'no_output_files': 'No output files generated',
        'corrupted_files': 'Output files corrupted or badly formatted',
        'incomplete_trajectory': 'Incomplete trajectory',
        'unexpected_artifacts': 'Unexpected artifacts in trajectory',
        'low_accuracy': 'Lower than expected accuracy',
        'bad_reliability': 'Bad reliability estimation',
        'partially_faulty': 'Parts of trajectory faulty',
        'entirely_faulty': 'Entire trajectory faulty',
        // Job reasons
        'most_failed': 'Most/all datasets failed',
        'empty_files': 'Empty or corrupted output files',
        'output_question': 'Question about output files',
        'template_question': 'Question about template settings',
        'parameter_tuning': 'Parameter tuning inquiry',
        'sensor_setup': 'Sensor setup optimization',
        // General reasons
        'account_question': 'Account or plan question',
        'invoice_question': 'Invoice or payment question',
        'cannot_create_jobs': 'Unable to create jobs',
        'new_sensor_config': 'New sensor configuration setup',
        'broken_pipeline': 'Previously working pipeline broken',
        // Other
        'other': 'Other issue'
      };
      return reasonLabels[reasonValue] || reasonValue;
    };

    // For "other" reason, use the first line of additionalInfo as title, rest as description
    let title: string;
    let description: string | undefined;

    if (reason === 'other' && additionalInfo.trim()) {
      const lines = additionalInfo.trim().split('\n');
      title = lines[0].substring(0, 100); // Limit title length
      description = lines.length > 1 ? lines.slice(1).join('\n').trim() || undefined : undefined;
    } else {
      title = getReasonLabel(reason);
      description = additionalInfo.trim() || undefined;
    }

    try {
      await createTicket.mutateAsync({
        title,
        description,
        priority: 'medium', // Default priority for support requests
        targets: targets.length > 0 ? targets : undefined
      });
      onClose(); // Close the support dialog after creating ticket
    } catch (error) {
      console.error('Failed to create support ticket:', error);
      // You could add error handling UI here if needed
    }
  };

  // Generate dialog title based on support type
  const getDialogTitle = () => {
    if (supportType === 'dataset') {
      return `Request Support for Dataset ${datasetName || 'Unknown'}${taskId ? ` (Task #${taskId})` : ''}`;
    } else if (supportType === 'job') {
      return `Request Support for Job${jobId ? ` #${jobId}` : ''}`;
    } else {
      return 'General Support Request';
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
      aria-labelledby="support-dialog-title"
    >
      <DialogTitle id="support-dialog-title">
        {getDialogTitle()}
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        {/* Task Support - Only shown for dataset support type */}
        {supportType === 'dataset' && (
          <Box sx={{ pt: 2 }}>
            <DialogContentText gutterBottom>
              Please describe the issue you're experiencing with the selected dataset.
            </DialogContentText>

            <FormControl component="fieldset" sx={{ mt: 2, width: '100%' }}>
              <FormLabel component="legend">Issue Type</FormLabel>
              <RadioGroup
                value={reason}
                onChange={onReasonChange}
              >
                <FormControlLabel value="processing_terminated" control={<Radio />} label="The processing has terminated with some error message." />
                <FormControlLabel value="no_output_files" control={<Radio />} label="No output files have been generated." />
                <FormControlLabel value="corrupted_files" control={<Radio />} label="The output files seem corrupted or badly formatted." />
                <FormControlLabel value="incomplete_trajectory" control={<Radio />} label="The trajectory appears to be incomplete." />
                <FormControlLabel value="unexpected_artifacts" control={<Radio />} label="There are one or more unexpected artifacts in the resulting trajectory." />
                <FormControlLabel value="low_accuracy" control={<Radio />} label="Overall, I was expecting a higher accuracy." />
                <FormControlLabel value="bad_reliability" control={<Radio />} label="Bad reliability: The estimated accuracy appears clearly too optimistic." />
                <FormControlLabel value="partially_faulty" control={<Radio />} label="Parts of the trajectory seem to be faulty, e.g. shifted, rugged, or unsteady." />
                <FormControlLabel value="entirely_faulty" control={<Radio />} label="The entire trajectory seems to be faulty, e.g. shifted, rugged, or unsteady." />
                <FormControlLabel value="other" control={<Radio />} label="Other" />
              </RadioGroup>
            </FormControl>
          </Box>
        )}

        {/* Job Support - Only shown for job support type */}
        {supportType === 'job' && (
          <Box sx={{ pt: 2 }}>
            <DialogContentText gutterBottom>
              Please describe the issue you're experiencing with this job.
            </DialogContentText>

            <FormControl component="fieldset" sx={{ mt: 2, width: '100%' }}>
              <FormLabel component="legend">Issue Type</FormLabel>
              <RadioGroup
                value={reason}
                onChange={onReasonChange}
              >
                <FormControlLabel value="most_failed" control={<Radio />} label="Most/all data sets have failed with some error messages." />
                <FormControlLabel value="empty_files" control={<Radio />} label="The processing seemed ok at first, but I only get empty or corrupted output files." />
                <FormControlLabel value="output_question" control={<Radio />} label="I have a question regarding the output files (format, data rate, contents)." />
                <FormControlLabel value="template_question" control={<Radio />} label="I have a general question regarding some template setting." />
                <FormControlLabel value="parameter_tuning" control={<Radio />} label="The results seem ok - I am wondering, if parameter tuning can improve things further." />
                <FormControlLabel value="sensor_setup" control={<Radio />} label="Can you suggest any extension/optimization of my positioning sensor setup?" />
                <FormControlLabel value="other" control={<Radio />} label="Other" />
              </RadioGroup>
            </FormControl>
          </Box>
        )}

        {/* General Support - Only shown for general support type */}
        {supportType === 'general' && (
          <Box sx={{ pt: 2 }}>
            <DialogContentText gutterBottom>
              Please describe your general support request.
            </DialogContentText>

            <FormControl component="fieldset" sx={{ mt: 2, width: '100%' }}>
              <FormLabel component="legend">Issue Type</FormLabel>
              <RadioGroup
                value={reason}
                onChange={onReasonChange}
              >
                <FormControlLabel value="account_question" control={<Radio />} label="I have a question regarding my plan or account details." />
                <FormControlLabel value="invoice_question" control={<Radio />} label="I have a question regarding my invoice / payments." />
                <FormControlLabel value="cannot_create_jobs" control={<Radio />} label="I am not able to create new jobs." />
                <FormControlLabel value="new_sensor_config" control={<Radio />} label="I have a new sensor configuration and require the set-up of a new positioning pipeline." />
                <FormControlLabel value="broken_pipeline" control={<Radio />} label="It seems my previously working positioning pipeline is broken somehow." />
                <FormControlLabel value="other" control={<Radio />} label="Other" />
              </RadioGroup>
            </FormControl>
          </Box>
        )}

        <TextField
          label={reason === 'other' ? "Please describe your issue (required)" : "Provide more details as required"}
          multiline
          rows={4}
          value={additionalInfo}
          onChange={onAdditionalInfoChange}
          fullWidth
          margin="normal"
          variant="outlined"
          placeholder={reason === 'other' ? "Please describe your issue in detail" : "Additional details about your support request (optional)"}
          aria-label="Additional information about the support request"
          required={reason === 'other'}
          error={reason === 'other' && !additionalInfo.trim()}
          helperText={reason === 'other' && !additionalInfo.trim() ? "This field is required when 'Other' is selected" : ""}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="inherit">Cancel</Button>
        <Button
          onClick={handleSupportRequestSubmit}
          variant="contained"
          color="primary"
          autoFocus
          disabled={createTicket.isPending || !reason || (reason === 'other' && !additionalInfo.trim())}
        >
          {createTicket.isPending ? 'Creating Ticket...' : 'Submit Support Request'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SupportDialog;
